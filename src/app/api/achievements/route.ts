import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Achievement from '@/models/Achievement';
import Employee from '@/models/Employee';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const employee = searchParams.get('employee');
    const rarity = searchParams.get('rarity');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');

    let query: any = { isPublic: true };

    if (type) {
      query.type = type;
    }

    if (category) {
      query.category = { $regex: category, $options: 'i' };
    }

    if (employee) {
      query.employee = employee;
    }

    if (rarity) {
      query['badge.rarity'] = rarity;
    }

    const achievements = await Achievement.find(query)
      .populate('employee', 'name email image department position')
      .select('-__v')
      .limit(limit)
      .skip((page - 1) * limit)
      .sort({ dateEarned: -1 });

    const total = await Achievement.countDocuments(query);

    return NextResponse.json({
      achievements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching achievements:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const body = await request.json();
    const { 
      employee, 
      title, 
      description, 
      type, 
      category, 
      dateEarned,
      isPublic = true,
      aiGenerated = false,
      metadata = {},
      badge = {
        icon: '🏆',
        color: '#FFD700',
        rarity: 'common'
      }
    } = body;

    // Validate required fields
    if (!employee || !title || !description || !type || !category) {
      return NextResponse.json(
        { error: 'Employee, title, description, type, and category are required' },
        { status: 400 }
      );
    }

    // Validate that employee exists
    const employeeRecord = await Employee.findById(employee);
    if (!employeeRecord) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Get current user to verify they can create achievements for this employee
    const currentEmployee = await Employee.findOne({ email: session.user.email });
    if (!currentEmployee) {
      return NextResponse.json(
        { error: 'Current user employee record not found' },
        { status: 404 }
      );
    }

    // Only allow users to create achievements for themselves
    if (employee !== currentEmployee._id.toString()) {
      return NextResponse.json(
        { error: 'You can only create achievements for yourself' },
        { status: 403 }
      );
    }

    const achievementData = {
      organization: employeeRecord.organization,
      employee,
      title: title.trim(),
      description: description.trim(),
      type,
      category: category.trim(),
      dateEarned: dateEarned ? new Date(dateEarned) : new Date(),
      isPublic,
      aiGenerated,
      metadata,
      badge: {
        icon: badge.icon || '🏆',
        color: badge.color || '#FFD700',
        rarity: badge.rarity || 'common'
      }
    };

    const achievement = new Achievement(achievementData);
    await achievement.save();

    // Populate the response
    await achievement.populate('employee', 'name email image department position');

    return NextResponse.json(achievement, { status: 201 });

  } catch (error) {
    console.error('Error creating achievement:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
