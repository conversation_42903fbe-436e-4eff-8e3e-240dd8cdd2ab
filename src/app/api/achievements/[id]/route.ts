import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Achievement from '@/models/Achievement';
import Employee from '@/models/Employee';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { id } = await params;
    const achievement = await Achievement.findById(id)
      .populate('employee', 'name email image department position');

    if (!achievement) {
      return NextResponse.json({ error: 'Achievement not found' }, { status: 404 });
    }

    // Check if user owns this achievement or has admin access
    const currentEmployee = await Employee.findOne({ email: session.user.email });
    if (!currentEmployee) {
      return NextResponse.json({ error: 'Employee record not found' }, { status: 404 });
    }

    // Check organization access (if organization field exists)
    if (achievement.organization && currentEmployee.organization &&
        achievement.organization.toString() !== currentEmployee.organization.toString()) {
      return NextResponse.json({ error: 'Access denied - different organization' }, { status: 403 });
    }

    // Only allow access if it's the user's own achievement or if it's public
    if (achievement.employee._id.toString() !== currentEmployee._id.toString() && !achievement.isPublic) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json(achievement);

  } catch (error) {
    console.error('Error fetching achievement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { id } = await params;
    const achievement = await Achievement.findById(id);
    if (!achievement) {
      return NextResponse.json({ error: 'Achievement not found' }, { status: 404 });
    }

    // Check if user owns this achievement
    const currentEmployee = await Employee.findOne({ email: session.user.email });
    if (!currentEmployee) {
      return NextResponse.json({ error: 'Employee record not found' }, { status: 404 });
    }

    // Check organization access (if organization field exists)
    if (achievement.organization && currentEmployee.organization &&
        achievement.organization.toString() !== currentEmployee.organization.toString()) {
      return NextResponse.json({ error: 'Access denied - different organization' }, { status: 403 });
    }

    if (achievement.employee.toString() !== currentEmployee._id.toString()) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      title, 
      description, 
      type, 
      category, 
      dateEarned,
      isPublic,
      metadata,
      badge
    } = body;

    // Validate required fields
    if (!title || !description || !type || !category) {
      return NextResponse.json(
        { error: 'Title, description, type, and category are required' },
        { status: 400 }
      );
    }

    // Update achievement
    const updatedAchievement = await Achievement.findByIdAndUpdate(
      id,
      {
        title: title.trim(),
        description: description.trim(),
        type,
        category: category.trim(),
        dateEarned: dateEarned ? new Date(dateEarned) : achievement.dateEarned,
        isPublic: isPublic !== undefined ? isPublic : achievement.isPublic,
        metadata: metadata || achievement.metadata,
        badge: badge ? {
          icon: badge.icon || achievement.badge.icon,
          color: badge.color || achievement.badge.color,
          rarity: badge.rarity || achievement.badge.rarity
        } : achievement.badge
      },
      { new: true, runValidators: true }
    ).populate('employee', 'name email image department position');

    return NextResponse.json(updatedAchievement);

  } catch (error) {
    console.error('Error updating achievement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { id } = await params;
    const achievement = await Achievement.findById(id);
    if (!achievement) {
      return NextResponse.json({ error: 'Achievement not found' }, { status: 404 });
    }

    // Check if user owns this achievement
    const currentEmployee = await Employee.findOne({ email: session.user.email });
    if (!currentEmployee) {
      return NextResponse.json({ error: 'Employee record not found' }, { status: 404 });
    }

    // Check organization access (if organization field exists)
    if (achievement.organization && currentEmployee.organization &&
        achievement.organization.toString() !== currentEmployee.organization.toString()) {
      return NextResponse.json({ error: 'Access denied - different organization' }, { status: 403 });
    }

    if (achievement.employee.toString() !== currentEmployee._id.toString()) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    await Achievement.findByIdAndDelete(id);

    return NextResponse.json({ message: 'Achievement deleted successfully' });

  } catch (error) {
    console.error('Error deleting achievement:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
