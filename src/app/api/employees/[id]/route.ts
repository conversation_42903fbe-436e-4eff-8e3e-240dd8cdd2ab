import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Achievement from '@/models/Achievement';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;

    // For public bio access, find employee by ID without organization restriction
    // This allows public bio pages to work even when users are logged out
    const employee = await Employee.findOne({
      _id: id,
      isActive: true // Only show active employees
    })
    .populate('organization', 'name domain') // Include organization info for context
    .select(
      'name email image bio department position startDate skills interests location linkedIn github organization'
    );

    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }

    // Fetch public achievements for this employee
    const achievements = await Achievement.find({
      employee: id,
      isPublic: true
    })
    .select('title description type category dateEarned badge metadata')
    .sort({ dateEarned: -1 })
    .limit(20); // Limit to most recent 20 achievements

    // Add achievements data to the response
    const employeeWithAchievements = {
      ...employee.toObject(),
      achievements: achievements,
      achievementCount: achievements.length
    };

    return NextResponse.json(employeeWithAchievements);
  } catch (error) {
    console.error('Error fetching employee:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
