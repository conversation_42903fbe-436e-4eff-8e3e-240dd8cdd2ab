import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Organization from '@/models/Organization';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const host = request.headers.get('host') || '';

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);

    // Find user in any organization
    const allUserInstances = await Employee.find({
      email: session.user.email
    }).populate('organization');

    // Find all organizations
    const allOrganizations = await Organization.find({}).select('name domain subdomain isActive');

    // Try to find user in the detected organization
    let userInDetectedOrg = null;
    if (orgContext) {
      userInDetectedOrg = await Employee.findOne({
        email: session.user.email,
        organization: orgContext.organization._id
      });
    }

    return NextResponse.json({
      debug: {
        host,
        sessionEmail: session.user.email,
        orgContext: orgContext ? {
          organizationId: orgContext.organization._id,
          organizationName: orgContext.organization.name,
          organizationDomain: orgContext.organization.domain,
          organizationSubdomain: orgContext.organization.subdomain,
          organizationActive: orgContext.organization.isActive,
          detectedDomain: orgContext.domain,
          detectedSubdomain: orgContext.subdomain
        } : null,
        userInDetectedOrg: userInDetectedOrg ? {
          id: userInDetectedOrg._id,
          name: userInDetectedOrg.name,
          email: userInDetectedOrg.email,
          organizationId: userInDetectedOrg.organization
        } : null,
        allUserInstances: allUserInstances.map(user => ({
          id: user._id,
          name: user.name,
          email: user.email,
          organizationId: user.organization?._id,
          organizationName: user.organization?.name,
          organizationDomain: user.organization?.domain
        })),
        allOrganizations: allOrganizations.map(org => ({
          id: org._id,
          name: org.name,
          domain: org.domain,
          subdomain: org.subdomain,
          isActive: org.isActive
        }))
      }
    });

  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { action } = await request.json();

    if (action === 'fix-production') {
      // Create achy.me organization if it doesn't exist
      let achyOrg = await Organization.findOne({ domain: 'achy.me' });

      if (!achyOrg) {
        achyOrg = await Organization.create({
          name: 'achy.me',
          domain: 'achy.me',
          subdomain: 'achy',
          isActive: true,
          subscription: {
            status: 'active',
            plan: 'pro',
            startDate: new Date(),
            endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
          },
          settings: {
            allowPublicThanks: true,
            requireApproval: false,
            emailNotifications: true
          }
        });
      }

      // Create user in achy.me organization if they don't exist
      let achyUser = await Employee.findOne({
        email: session.user.email,
        organization: achyOrg._id
      });

      if (!achyUser) {
        // Get user data from omantel.om organization
        const sourceUser = await Employee.findOne({
          email: session.user.email,
          organization: '685829ae365d401e5b0c9918' // omantel organization ID
        });

        if (sourceUser) {
          achyUser = await Employee.create({
            name: sourceUser.name,
            email: sourceUser.email,
            organization: achyOrg._id,
            role: sourceUser.role || 'employee',
            department: sourceUser.department || 'General',
            position: sourceUser.position || 'Employee',
            bio: sourceUser.bio || '',
            profileImage: sourceUser.profileImage || '',
            isActive: true,
            joinDate: sourceUser.joinDate || new Date()
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Production environment fixed',
        achyOrg: {
          id: achyOrg._id,
          name: achyOrg.name,
          domain: achyOrg.domain
        },
        achyUser: achyUser ? {
          id: achyUser._id,
          name: achyUser.name,
          email: achyUser.email
        } : null
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Fix error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
