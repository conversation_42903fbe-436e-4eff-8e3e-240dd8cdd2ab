import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import Organization from '@/models/Organization';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const host = request.headers.get('host') || '';
    
    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    
    // Find user in any organization
    const allUserInstances = await Employee.find({
      email: session.user.email
    }).populate('organization');

    // Find all organizations
    const allOrganizations = await Organization.find({}).select('name domain subdomain isActive');

    // Try to find user in the detected organization
    let userInDetectedOrg = null;
    if (orgContext) {
      userInDetectedOrg = await Employee.findOne({
        email: session.user.email,
        organization: orgContext.organization._id
      });
    }

    return NextResponse.json({
      debug: {
        host,
        sessionEmail: session.user.email,
        orgContext: orgContext ? {
          organizationId: orgContext.organization._id,
          organizationName: orgContext.organization.name,
          organizationDomain: orgContext.organization.domain,
          organizationSubdomain: orgContext.organization.subdomain,
          organizationActive: orgContext.organization.isActive,
          detectedDomain: orgContext.domain,
          detectedSubdomain: orgContext.subdomain
        } : null,
        userInDetectedOrg: userInDetectedOrg ? {
          id: userInDetectedOrg._id,
          name: userInDetectedOrg.name,
          email: userInDetectedOrg.email,
          organizationId: userInDetectedOrg.organization
        } : null,
        allUserInstances: allUserInstances.map(user => ({
          id: user._id,
          name: user.name,
          email: user.email,
          organizationId: user.organization?._id,
          organizationName: user.organization?.name,
          organizationDomain: user.organization?.domain
        })),
        allOrganizations: allOrganizations.map(org => ({
          id: org._id,
          name: org.name,
          domain: org.domain,
          subdomain: org.subdomain,
          isActive: org.isActive
        }))
      }
    });

  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
