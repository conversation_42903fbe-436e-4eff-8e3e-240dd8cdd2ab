'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Navigation from '@/components/Navigation';
import { motion, AnimatePresence } from 'framer-motion';

interface Employee {
  _id: string;
  name: string;
  email: string;
  image?: string;
  department?: string;
  position?: string;
}

interface Achievement {
  _id: string;
  employee: Employee;
  title: string;
  description: string;
  type: string;
  category: string;
  dateEarned: string;
  isPublic: boolean;
  aiGenerated: boolean;
  metadata: any;
  badge: {
    icon: string;
    color: string;
    rarity: string;
  };
}

const getRarityColor = (rarity: string) => {
  switch (rarity) {
    case 'common':
      return 'bg-gray-100 text-gray-800 border-gray-300';
    case 'uncommon':
      return 'bg-green-100 text-green-800 border-green-300';
    case 'rare':
      return 'bg-blue-100 text-blue-800 border-blue-300';
    case 'epic':
      return 'bg-purple-100 text-purple-800 border-purple-300';
    case 'legendary':
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-300';
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'milestone':
      return '🎯';
    case 'recognition':
      return '👏';
    case 'skill':
      return '🧠';
    case 'project':
      return '🚀';
    case 'anniversary':
      return '🎂';
    default:
      return '⭐';
  }
};

export default function AchievementsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [myAchievements, setMyAchievements] = useState<Achievement[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAchievement, setEditingAchievement] = useState<Achievement | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    } else if (status === 'authenticated') {
      fetchMyAchievements();
    }
  }, [status, router]);

  const fetchMyAchievements = async () => {
    try {
      const response = await fetch('/api/achievements/me');
      if (response.ok) {
        const data = await response.json();
        // The API returns { achievements: [...], stats: {...}, pagination: {...} }
        setMyAchievements(data.achievements || []);
      } else {
        console.error('Failed to fetch my achievements:', response.status);
        setMyAchievements([]);
      }
    } catch (error) {
      console.error('Error fetching my achievements:', error);
      setMyAchievements([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddAchievement = async (achievementData: any) => {
    setSaving(true);
    try {
      const response = await fetch('/api/achievements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(achievementData),
      });

      if (response.ok) {
        const newAchievement = await response.json();
        setMyAchievements(prev => [newAchievement, ...prev]);
        setShowAddModal(false);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to create achievement');
      }
    } catch (error) {
      console.error('Error creating achievement:', error);
      alert('Failed to create achievement');
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateAchievement = async (id: string, achievementData: any) => {
    setSaving(true);
    try {
      const response = await fetch(`/api/achievements/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(achievementData),
      });

      if (response.ok) {
        const updatedAchievement = await response.json();
        setMyAchievements(prev =>
          prev.map(achievement =>
            achievement._id === id ? updatedAchievement : achievement
          )
        );
        setEditingAchievement(null);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to update achievement');
      }
    } catch (error) {
      console.error('Error updating achievement:', error);
      alert('Failed to update achievement');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteAchievement = async (id: string) => {
    if (!confirm('Are you sure you want to delete this achievement?')) {
      return;
    }

    try {
      const response = await fetch(`/api/achievements/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setMyAchievements(prev => prev.filter(achievement => achievement._id !== id));
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to delete achievement');
      }
    } catch (error) {
      console.error('Error deleting achievement:', error);
      alert('Failed to delete achievement');
    }
  };



  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="max-w-6xl mx-auto py-8 px-4">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white shadow rounded-lg p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const displayAchievements = Array.isArray(myAchievements) ? myAchievements : [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Navigation />
      <div className="max-w-6xl mx-auto py-8 px-4">
        <div className="mb-8">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                My Achievements
              </h1>
              <p className="text-gray-600 text-lg">
                Track your professional accomplishments and milestones
              </p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-200 flex items-center space-x-2"
            >
              <span>➕</span>
              <span>Add Achievement</span>
            </button>
          </div>
          {Array.isArray(myAchievements) && myAchievements.length > 0 && (
            <div className="mt-4 bg-white rounded-xl shadow-md p-4 border border-gray-100">
              <div className="flex items-center justify-between">
                <span className="text-gray-700 font-medium">Total Achievements</span>
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full font-bold">
                  {myAchievements.length}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Achievement Stats */}
        {Array.isArray(myAchievements) && myAchievements.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
            {['common', 'uncommon', 'rare', 'epic', 'legendary'].map((rarity) => {
              const count = myAchievements.filter(a => a.badge.rarity === rarity).length;
              return count > 0 ? (
                <div key={rarity} className={`p-6 rounded-xl border-2 shadow-md hover:shadow-lg transition-all duration-200 ${getRarityColor(rarity)}`}>
                  <div className="text-3xl font-bold mb-1">{count}</div>
                  <div className="text-sm font-medium capitalize">{rarity}</div>
                </div>
              ) : null;
            })}
          </div>
        )}

        {/* Achievements Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayAchievements.length === 0 ? (
            <div className="col-span-full bg-white shadow-lg rounded-2xl p-12 text-center border border-gray-100">
              <div className="text-6xl mb-4">🏆</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Achievements Yet</h3>
              <p className="text-gray-600">
                Keep up the great work! Your achievements will appear here as you reach new milestones.
              </p>
            </div>
          ) : (
            displayAchievements.map((achievement, index) => (
              <div
                key={achievement._id}
                className={`bg-white shadow-lg rounded-2xl p-6 border-2 hover:shadow-xl transition-all duration-300 hover:scale-105 transform`}
                style={{
                  borderColor: achievement.badge.color,
                  animationDelay: `${index * 0.1}s`
                }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div
                      className="w-16 h-16 rounded-2xl flex items-center justify-center text-3xl shadow-md"
                      style={{ backgroundColor: achievement.badge.color + '20' }}
                    >
                      {achievement.badge.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-gray-900 text-lg mb-1">{achievement.title}</h3>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          {getTypeIcon(achievement.type)} {achievement.type}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-bold border-2 ${getRarityColor(achievement.badge.rarity)}`}>
                      {achievement.badge.rarity.toUpperCase()}
                    </span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingAchievement(achievement)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Edit Achievement"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleDeleteAchievement(achievement._id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete Achievement"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>

                <p className="text-gray-700 mb-6 leading-relaxed">{achievement.description}</p>

                <div className="flex items-center justify-between text-sm mb-4">
                  <span
                    className="px-3 py-1 rounded-full text-white font-medium shadow-sm"
                    style={{ backgroundColor: achievement.badge.color }}
                  >
                    {achievement.category}
                  </span>
                  <div className="flex items-center space-x-2">
                    {achievement.aiGenerated && (
                      <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        AI-generated
                      </span>
                    )}
                    <span className="text-gray-500 font-medium">
                      {new Date(achievement.dateEarned).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </span>
                  </div>
                </div>

                {/* Metadata Display */}
                {achievement.metadata && Object.keys(achievement.metadata).length > 0 && (
                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex flex-wrap gap-2">
                      {achievement.metadata.thanksCount && (
                        <span className="bg-pink-100 text-pink-700 px-3 py-1 rounded-full text-xs font-medium">
                          💝 {achievement.metadata.thanksCount} Thanks
                        </span>
                      )}
                      {achievement.metadata.projectName && (
                        <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-medium">
                          🚀 {achievement.metadata.projectName}
                        </span>
                      )}
                      {achievement.metadata.skillLevel && (
                        <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium">
                          📈 {achievement.metadata.skillLevel}
                        </span>
                      )}
                      {achievement.metadata.yearsOfService && (
                        <span className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-xs font-medium">
                          🎂 {achievement.metadata.yearsOfService} Years
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Add/Edit Achievement Modal */}
        <AnimatePresence>
          {(showAddModal || editingAchievement) && (
            <AchievementModal
              achievement={editingAchievement}
              onSave={editingAchievement ?
                (data) => handleUpdateAchievement(editingAchievement._id, data) :
                handleAddAchievement
              }
              onClose={() => {
                setShowAddModal(false);
                setEditingAchievement(null);
              }}
              saving={saving}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}

// Achievement Modal Component
interface AchievementModalProps {
  achievement?: Achievement | null;
  onSave: (data: any) => void;
  onClose: () => void;
  saving: boolean;
}

function AchievementModal({ achievement, onSave, onClose, saving }: AchievementModalProps) {
  const [formData, setFormData] = useState({
    title: achievement?.title || '',
    description: achievement?.description || '',
    type: achievement?.type || 'recognition',
    category: achievement?.category || '',
    dateEarned: achievement?.dateEarned ? new Date(achievement.dateEarned).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    isPublic: achievement?.isPublic !== undefined ? achievement.isPublic : true,
    badge: {
      icon: achievement?.badge?.icon || '🏆',
      color: achievement?.badge?.color || '#FFD700',
      rarity: achievement?.badge?.rarity || 'common'
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.description.trim() || !formData.category.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    // Get current user's employee ID
    try {
      const response = await fetch('/api/employees/me');
      if (!response.ok) {
        alert('Failed to get user information');
        return;
      }
      const userData = await response.json();

      onSave({
        ...formData,
        employee: userData._id
      });
    } catch (error) {
      console.error('Error getting user data:', error);
      alert('Failed to get user information');
    }
  };

  const achievementTypes = [
    { value: 'milestone', label: '🎯 Milestone', icon: '🎯' },
    { value: 'recognition', label: '👏 Recognition', icon: '👏' },
    { value: 'skill', label: '🧠 Skill', icon: '🧠' },
    { value: 'project', label: '🚀 Project', icon: '🚀' },
    { value: 'anniversary', label: '🎂 Anniversary', icon: '🎂' },
    { value: 'custom', label: '⭐ Custom', icon: '⭐' }
  ];

  const rarityOptions = [
    { value: 'common', label: 'Common', color: '#6B7280' },
    { value: 'uncommon', label: 'Uncommon', color: '#10B981' },
    { value: 'rare', label: 'Rare', color: '#3B82F6' },
    { value: 'epic', label: 'Epic', color: '#8B5CF6' },
    { value: 'legendary', label: 'Legendary', color: '#F59E0B' }
  ];

  const iconOptions = ['🏆', '🎖️', '🥇', '⭐', '💎', '🔥', '⚡', '🎯', '🚀', '💪', '🧠', '❤️'];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {achievement ? 'Edit Achievement' : 'Add New Achievement'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter achievement title"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe the achievement"
              required
            />
          </div>

          {/* Type and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type *
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                {achievementTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <input
                type="text"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Leadership, Innovation"
                required
              />
            </div>
          </div>

          {/* Date and Visibility */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date Earned
              </label>
              <input
                type="date"
                value={formData.dateEarned}
                onChange={(e) => setFormData(prev => ({ ...prev, dateEarned: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex items-center space-x-3 pt-8">
              <input
                type="checkbox"
                id="isPublic"
                checked={formData.isPublic}
                onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}
                className="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="isPublic" className="text-sm font-medium text-gray-700">
                Make this achievement public
              </label>
            </div>
          </div>

          {/* Badge Configuration */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Badge Configuration</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Icon */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Icon
                </label>
                <div className="grid grid-cols-6 gap-2">
                  {iconOptions.map(icon => (
                    <button
                      key={icon}
                      type="button"
                      onClick={() => setFormData(prev => ({
                        ...prev,
                        badge: { ...prev.badge, icon }
                      }))}
                      className={`p-2 text-xl border rounded-lg hover:bg-gray-50 ${
                        formData.badge.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              {/* Color */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Color
                </label>
                <input
                  type="color"
                  value={formData.badge.color}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    badge: { ...prev.badge, color: e.target.value }
                  }))}
                  className="w-full h-12 border border-gray-300 rounded-lg cursor-pointer"
                />
              </div>

              {/* Rarity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rarity
                </label>
                <select
                  value={formData.badge.rarity}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    badge: { ...prev.badge, rarity: e.target.value as any }
                  }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {rarityOptions.map(rarity => (
                    <option key={rarity.value} value={rarity.value}>
                      {rarity.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Preview */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">Preview:</p>
              <div className="flex items-center space-x-3">
                <div
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl"
                  style={{ backgroundColor: formData.badge.color + '20' }}
                >
                  {formData.badge.icon}
                </div>
                <div>
                  <div className="font-semibold">{formData.title || 'Achievement Title'}</div>
                  <span className={`px-2 py-1 rounded-full text-xs font-bold ${getRarityColor(formData.badge.rarity)}`}>
                    {formData.badge.rarity.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50"
            >
              {saving ? 'Saving...' : (achievement ? 'Update Achievement' : 'Create Achievement')}
            </button>
          </div>
        </form>
      </motion.div>
    </motion.div>
  );
}
