'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import Navigation from '@/components/Navigation';
import {
  Heart,
  Calendar,
  Award,
  Smile,
  ThumbsUp,
  Star,
  Zap,
  User,
  Users,
  Eye,
  EyeOff,
  Lock,
  ArrowLeft,
  Share2,
  Send,
  X
} from 'lucide-react';
import Link from 'next/link';

interface Employee {
  _id: string;
  name: string;
  email: string;
  image?: string;
  department?: string;
  position?: string;
}

interface Thanks {
  _id: string;
  message: string;
  category: string;
  fromEmployee?: Employee;
  toEmployee: Employee;
  anonymousSender?: {
    name: string;
  };
  isPublic: boolean;
  aiGenerated?: boolean;
  reactions: Array<{
    emoji: string;
    count: number;
    users: string[];
  }>;
  createdAt: string;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

export default function UserThanksPage({ params }: { params: { userId: string } }) {
  const { data: session } = useSession();
  const [thanks, setThanks] = useState<Thanks[]>([]);
  const [targetEmployee, setTargetEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<Employee | null>(null);
  const [privacyFilter, setPrivacyFilter] = useState<'all' | 'public' | 'private'>('all');
  const [showThanksForm, setShowThanksForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [anonymousName, setAnonymousName] = useState('');
  const [messageLength, setMessageLength] = useState(0);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  const fetchTargetEmployee = async () => {
    if (!params?.userId) return;
    try {
      setLoading(true);
      const response = await fetch(`/api/thanks/user/${params.userId}`);
      if (response.ok) {
        const data = await response.json();
        setTargetEmployee(data.targetEmployee);
        setThanks(data.thanks || []);
      } else {
        console.error('Employee not found');
      }
    } catch (error) {
      console.error('Error fetching employee and thanks:');
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchTargetEmployee();
  }, [params.userId]);

  useEffect(() => {
    if (session?.user?.email) {
      fetchCurrentUser();
    }
  }, [session]);

  const fetchCurrentUser = async () => {
    try {
      const response = await fetch('/api/employees/me');
      if (response.ok) {
        const userData = await response.json();
        setCurrentUser(userData);
      }
    } catch (error) {
      console.error('Error fetching current user:', error);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      teamwork: 'bg-blue-50 text-blue-700 border-blue-200',
      leadership: 'bg-purple-50 text-purple-700 border-purple-200',
      innovation: 'bg-green-50 text-green-700 border-green-200',
      support: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      achievement: 'bg-red-50 text-red-700 border-red-200',
      mentorship: 'bg-indigo-50 text-indigo-700 border-indigo-200',
      collaboration: 'bg-pink-50 text-pink-700 border-pink-200',
      dedication: 'bg-orange-50 text-orange-700 border-orange-200'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-50 text-gray-700 border-gray-200';
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      teamwork: <Users className="w-3 h-3" />,
      leadership: <Award className="w-3 h-3" />,
      innovation: <Zap className="w-3 h-3" />,
      support: <Heart className="w-3 h-3" />,
      achievement: <Star className="w-3 h-3" />,
      mentorship: <User className="w-3 h-3" />,
      collaboration: <ThumbsUp className="w-3 h-3" />,
      dedication: <Smile className="w-3 h-3" />
    };
    return icons[category as keyof icons] || <Heart className="w-3 h-3" />;
  };

  const handleShare = async () => {
    const url = window.location.href;
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Thanks for ${targetEmployee?.name}`,
          text: `Check out the appreciation messages for ${targetEmployee?.name}`,
          url: url,
        });
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(url);
        alert('Link copied to clipboard!');
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(url);
      alert('Link copied to clipboard!');
    }
  };

  const handleSubmitThanks = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!targetEmployee) return;

    setSubmitting(true);

    const formData = new FormData(e.currentTarget);
    const thanksData = {
      toEmployee: targetEmployee._id,
      message: formData.get('message'),
      category: formData.get('category'),
      isPublic: formData.get('isPublic') === 'on',
      // Add anonymous sender info since this is for guests
      anonymousSender: {
        name: anonymousName.trim() || 'Anonymous'
      }
    };

    // Validate required fields
    if (!thanksData.message || !thanksData.category) {
      alert('Please fill in all required fields');
      setSubmitting(false);
      return;
    }

    // Validate anonymous name
    if (!anonymousName.trim()) {
      alert('Please enter your name');
      setSubmitting(false);
      return;
    }

    // Validate message length
    if (typeof thanksData.message === 'string' && thanksData.message.trim().length < 10) {
      alert('Please write a message with at least 10 characters');
      setSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/thanks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(thanksData),
      });

      if (response.ok) {
        setShowThanksForm(false);
        setAnonymousName('');
        setMessageLength(0);
        fetchTargetEmployee(); // Refresh the thanks list
        (e.target as HTMLFormElement).reset();
        alert('Thanks sent successfully!');
      } else {
        const errorData = await response.json();
        alert(`Failed to send thanks: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error sending thanks:', error);
      alert('Error sending thanks. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handlePrivacyToggle = async (thanksId: string, currentIsPublic: boolean) => {
    try {
      const response = await fetch(`/api/thanks/${thanksId}/privacy`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isPublic: !currentIsPublic }),
      });

      if (response.ok) {
        const data = await response.json();
        // Update the thanks list with new privacy status
        setThanks(prevThanks =>
          prevThanks.map(thank =>
            thank._id === thanksId
              ? { ...thank, isPublic: !currentIsPublic }
              : thank
          )
        );

        // Show feedback to user
        const status = !currentIsPublic ? 'public' : 'private';
        setToast({ message: `Thanks message is now ${status}`, type: 'success' });
        setTimeout(() => setToast(null), 3000);
      } else {
        const errorData = await response.json();
        setToast({ message: `Failed to update privacy: ${errorData.error || 'Unknown error'}`, type: 'error' });
        setTimeout(() => setToast(null), 3000);
      }
    } catch (error) {
      console.error('Error updating privacy:', error);
      setToast({ message: 'Error updating privacy. Please try again.', type: 'error' });
      setTimeout(() => setToast(null), 3000);
    }
  };

  if (!params?.userId) {
    return <div>Loading...</div>;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
        <Navigation />
        <div className="max-w-6xl mx-auto py-8 px-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading thanks...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!targetEmployee) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
        <Navigation />
        <div className="max-w-6xl mx-auto py-8 px-4">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Employee Not Found</h1>
            <p className="text-gray-600 mb-6">The employee you're looking for doesn't exist or has been removed.</p>
            <Link
              href="/thanks"
              className="inline-flex items-center space-x-2 bg-primary-500 text-white px-6 py-3 rounded-xl hover:bg-primary-600 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Thanks</span>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Apply privacy filter
  let filteredThanks = thanks;
  if (currentUser && privacyFilter !== 'all') {
    filteredThanks = thanks.filter(thank => {
      if (privacyFilter === 'public') {
        return thank.isPublic;
      } else if (privacyFilter === 'private') {
        return !thank.isPublic && (
          thank.toEmployee._id === currentUser._id || 
          (thank.fromEmployee && thank.fromEmployee._id === currentUser._id)
        );
      }
      return true;
    });
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
      <Navigation />
      <div className="max-w-6xl mx-auto py-8 px-4">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <Link
              href="/thanks"
              className="mr-4 p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </Link>
            <div className="flex-1">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent">
                Thanks for {targetEmployee.name}
              </h1>
              <p className="text-gray-600 mt-2">
                {targetEmployee.position && targetEmployee.department
                  ? `${targetEmployee.position} • ${targetEmployee.department}`
                  : targetEmployee.email
                }
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowThanksForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-full shadow-md hover:shadow-lg transition-all duration-200"
              >
                <Heart className="w-4 h-4" />
                <span>Send Thanks</span>
              </motion.button>
              <button
                onClick={handleShare}
                className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
              >
                <Share2 className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </motion.div>

        {/* Privacy Filter for Authenticated Users */}
        {currentUser && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 flex items-center justify-center space-x-2"
          >
            <span className="text-sm font-medium text-gray-600 mr-2">Filter:</span>
            {(['all', 'public', 'private'] as const).map((filter) => (
              <motion.button
                key={filter}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setPrivacyFilter(filter)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  privacyFilter === filter
                    ? 'bg-primary-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter === 'all' && 'All Thanks'}
                {filter === 'public' && 'Public Only'}
                {filter === 'private' && 'Private Only'}
              </motion.button>
            ))}
          </motion.div>
        )}

        {/* Privacy Information for Authenticated Users */}
        {currentUser && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 bg-blue-50 border border-blue-200 rounded-xl p-4"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <Eye className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-sm font-semibold text-blue-900">Privacy Controls</h3>
                <p className="text-sm text-blue-700">
                  {currentUser._id === targetEmployee?._id
                    ? "You can toggle the privacy of thanks directed to you using the Public/Private buttons below each message."
                    : "You can see all public thanks and private thanks where you're involved. Private thanks have a gray background."
                  }
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Thanks Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {filteredThanks.length === 0 ? (
            <motion.div
              variants={itemVariants}
              className="col-span-full bg-gradient-to-br from-pink-50 to-purple-50 shadow-lg rounded-3xl p-12 text-center border border-pink-200"
            >
              <div className="text-6xl mb-4"><Heart /></div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                No Thanks Yet for {targetEmployee.name}
              </h3>
              <p className="text-gray-600 text-lg mb-6">
                {targetEmployee.name} hasn't received any {privacyFilter === 'private' ? 'private' : privacyFilter === 'public' ? 'public' : ''} thanks yet.
                Be the first to show your appreciation!
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowThanksForm(true)}
                  className="inline-flex items-center space-x-2 bg-gradient-to-r from-pink-500 to-pink-600 text-white px-8 py-4 rounded-xl hover:shadow-lg transition-all duration-200 font-medium"
                >
                  <Heart className="w-5 h-5" />
                  <span>Send Thanks</span>
                </motion.button>
                <Link
                  href="/recognition"
                  className="inline-flex items-center space-x-2 bg-white text-gray-700 px-8 py-4 rounded-xl hover:bg-gray-50 transition-colors border border-gray-200 font-medium"
                >
                  <ArrowLeft className="w-5 h-5" />
                  <span>View All Recognition</span>
                </Link>
              </div>
            </motion.div>
          ) : (
            filteredThanks.map((thank, index) => {
              const isPrivateToUser = !thank.isPublic && currentUser && (
                thank.toEmployee._id === currentUser._id || 
                (thank.fromEmployee && thank.fromEmployee._id === currentUser._id)
              );
              
              return (
                <motion.div
                  key={thank._id}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02, y: -5 }}
                  className={`shadow-lg rounded-3xl p-6 border hover:shadow-xl transition-all duration-300 ${
                    isPrivateToUser 
                      ? 'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-300' 
                      : 'bg-white border-gray-100'
                  }`}
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                        {thank.fromEmployee?.name?.[0] || thank.anonymousSender?.name?.[0] || '?'}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {thank.fromEmployee?.name || thank.anonymousSender?.name || 'Anonymous'}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {thank.fromEmployee?.position || 'Anonymous Sender'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Categories and Privacy Status */}
                  <div className="flex flex-wrap items-center gap-2 mb-4">
                    <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium border ${getCategoryColor(thank.category)}`}>
                      {getCategoryIcon(thank.category)}
                      <span className="capitalize">{thank.category}</span>
                    </span>
                    <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium border ${
                      thank.isPublic
                        ? 'bg-green-50 text-green-700 border-green-200'
                        : 'bg-gray-50 text-gray-700 border-gray-200'
                    }`}>
                      {thank.isPublic ? (
                        <>
                          <Eye className="w-3 h-3" />
                          <span>Public</span>
                        </>
                      ) : (
                        <>
                          <Lock className="w-3 h-3" />
                          <span>Private</span>
                        </>
                      )}
                    </span>
                  </div>

                  {/* Message */}
                  <div className="mb-6">
                    <p className="text-gray-800 leading-relaxed text-lg">
                      {thank.message}
                    </p>
                  </div>

                  {/* Bottom Row */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2 text-gray-500">
                        <Calendar className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          {new Date(thank.createdAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                          })}
                        </span>
                      </div>

                      {/* Privacy Toggle - Only visible to the recipient */}
                      {currentUser && thank.toEmployee._id === currentUser._id && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handlePrivacyToggle(thank._id, thank.isPublic)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-full transition-all duration-200 text-sm font-medium border-2 ${
                            thank.isPublic
                              ? 'bg-green-100 hover:bg-green-200 text-green-700 border-green-300 hover:border-green-400'
                              : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300 hover:border-gray-400'
                          }`}
                          title={thank.isPublic ? 'Make Private' : 'Make Public'}
                        >
                          {thank.isPublic ? (
                            <>
                              <Eye className="w-4 h-4" />
                              <span>Public</span>
                            </>
                          ) : (
                            <>
                              <EyeOff className="w-4 h-4" />
                              <span>Private</span>
                            </>
                          )}
                        </motion.button>
                      )}
                    </div>
                  </div>
                </motion.div>
              );
            })
          )}
        </motion.div>
      </div>

      {/* Thanks Form Modal */}
      <AnimatePresence>
        {showThanksForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowThanksForm(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-gradient-to-r from-pink-500 to-pink-600 rounded-2xl">
                      <Heart className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Send Thanks</h2>
                      <p className="text-gray-600">to {targetEmployee?.name}</p>
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setShowThanksForm(false)}
                    className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
                  >
                    <X className="w-6 h-6 text-gray-500" />
                  </motion.button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmitThanks} className="space-y-6">
                  {/* Your Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Name *
                    </label>
                    <input
                      type="text"
                      value={anonymousName}
                      onChange={(e) => setAnonymousName(e.target.value)}
                      placeholder="Enter your name"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-colors"
                      required
                    />
                  </div>

                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category *
                    </label>
                    <select
                      name="category"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-colors"
                      required
                    >
                      <option value="">Select a category</option>
                      <option value="teamwork">Teamwork</option>
                      <option value="leadership">Leadership</option>
                      <option value="innovation">Innovation</option>
                      <option value="support">Support</option>
                      <option value="achievement">Achievement</option>
                      <option value="mentorship">Mentorship</option>
                      <option value="collaboration">Collaboration</option>
                      <option value="dedication">Dedication</option>
                    </select>
                  </div>

                  {/* Message */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Message *
                    </label>
                    <textarea
                      name="message"
                      rows={4}
                      placeholder="Write your appreciation message..."
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-colors resize-none"
                      onChange={(e) => setMessageLength(e.target.value.length)}
                      required
                    />
                    <div className="flex justify-between items-center mt-2">
                      <p className="text-xs text-gray-500">
                        Minimum 10 characters
                      </p>
                      <p className={`text-xs ${messageLength < 10 ? 'text-red-500' : 'text-gray-500'}`}>
                        {messageLength} characters
                      </p>
                    </div>
                  </div>

                  {/* Privacy */}
                  <div>
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        name="isPublic"
                        defaultChecked
                        className="w-4 h-4 text-pink-600 border-gray-300 rounded focus:ring-pink-500"
                      />
                      <span className="text-sm text-gray-700">
                        Make this thanks public (visible to everyone)
                      </span>
                    </label>
                  </div>

                  {/* Submit Button */}
                  <div className="flex space-x-3 pt-4">
                    <motion.button
                      type="button"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setShowThanksForm(false)}
                      className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors font-medium"
                    >
                      Cancel
                    </motion.button>
                    <motion.button
                      type="submit"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      disabled={submitting}
                      className="flex-1 px-6 py-3 bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-xl hover:shadow-lg transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                    >
                      {submitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Sending...</span>
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4" />
                          <span>Send Thanks</span>
                        </>
                      )}
                    </motion.button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toast Notification */}
      <AnimatePresence>
        {toast && (
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 50, scale: 0.9 }}
            className="fixed bottom-4 right-4 z-50"
          >
            <div className={`px-6 py-4 rounded-xl shadow-lg border-2 ${
              toast.type === 'success'
                ? 'bg-green-50 border-green-200 text-green-800'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center space-x-3">
                {toast.type === 'success' ? (
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                ) : (
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <X className="w-4 h-4 text-white" />
                  </div>
                )}
                <span className="font-medium">{toast.message}</span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
