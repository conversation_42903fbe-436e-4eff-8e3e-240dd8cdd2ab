/* Profile page specific styles with subtle colors */

/* Use a clean, minimal color scheme */
.profile-page {
  background: #ffffff !important;
  color: #171717 !important;
  min-height: 100vh !important;
}

.profile-page * {
  color: inherit !important;
}

/* Pure white background */
.profile-page .min-h-screen {
  background: #ffffff !important;
}

/* Clean white cards */
.profile-page .bg-white {
  background-color: #ffffff !important;
  color: #171717 !important;
}

/* Orange gradients for elements */
.profile-page .bg-gradient-to-r {
  background: linear-gradient(to right, #ff7800, #ea580c) !important;
}

.profile-page .bg-gradient-to-br {
  background: #ffffff !important;
}

/* Orange primary colors for elements */
.profile-page .bg-primary-50 {
  background-color: #fff7ed !important;
}

.profile-page .bg-primary-100 {
  background-color: #ffedd5 !important;
}

.profile-page .bg-primary-500 {
  background-color: #ff7800 !important;
}

.profile-page .bg-primary-600 {
  background-color: #ea580c !important;
}

/* Force text colors */
.profile-page .text-gray-600 {
  color: #4b5563 !important;
}

.profile-page .text-gray-700 {
  color: #374151 !important;
}

.profile-page .text-gray-900 {
  color: #111827 !important;
}

.profile-page .text-white {
  color: #ffffff !important;
}

.profile-page .text-primary-600 {
  color: #ea580c !important;
}

/* CRITICAL: Force white text in orange backgrounds for profile page */
.profile-page .bg-primary-500,
.profile-page .bg-primary-500 *,
.profile-page .bg-primary-600,
.profile-page .bg-primary-600 *,
.profile-page .bg-gradient-to-r,
.profile-page .bg-gradient-to-r * {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
}

/* Ensure buttons with orange backgrounds have white text on profile page */
.profile-page button.bg-primary-500,
.profile-page button.bg-primary-500 *,
.profile-page button.bg-primary-600,
.profile-page button.bg-primary-600 *,
.profile-page a.bg-primary-500,
.profile-page a.bg-primary-500 *,
.profile-page a.bg-primary-600,
.profile-page a.bg-primary-600 *,
.profile-page div[class*="bg-gradient-to-r"],
.profile-page div[class*="bg-gradient-to-r"] * {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
}

/* Orange gradient text */
.profile-page .text-transparent {
  background: linear-gradient(to right, #ff7800, #ea580c) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}

/* Force shadows and borders */
.profile-page .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.profile-page .shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.profile-page .border-gray-100 {
  border-color: #f3f4f6 !important;
}

/* Override any dark mode styles */
@media (prefers-color-scheme: dark) {
  .profile-page {
    background: #ffffff !important;
    color: #171717 !important;
  }
  
  .profile-page * {
    color: inherit !important;
  }
  
  .profile-page .bg-white {
    background-color: #ffffff !important;
    color: #171717 !important;
  }
}
