#!/usr/bin/env node

/**
 * Test Achievement CRUD Operations
 * This script tests the achievement CRUD functionality
 */

const BASE_URL = 'http://localhost:3001';

// Test data
const testAchievement = {
  title: "Test Achievement",
  description: "This is a test achievement created by the test script",
  type: "recognition",
  category: "Testing",
  dateEarned: new Date().toISOString().split('T')[0],
  isPublic: true,
  badge: {
    icon: "🧪",
    color: "#10B981",
    rarity: "rare"
  }
};

async function testEndpoint(method, endpoint, data = null, headers = {}) {
  try {
    console.log(`\n${method} ${endpoint}`);
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const responseData = await response.json();
      console.log(`Response: ${JSON.stringify(responseData, null, 2).substring(0, 200)}...`);
      return responseData;
    } else {
      const errorData = await response.text();
      console.log(`Error: ${errorData}`);
      return null;
    }
  } catch (error) {
    console.log(`Error: ${error.message}`);
    return null;
  }
}

async function main() {
  console.log('🧪 Testing Achievement CRUD Operations\n');
  console.log(`Base URL: ${BASE_URL}\n`);
  
  // Note: These tests require authentication
  // In a real scenario, you would need to authenticate first
  
  console.log('📝 Testing Achievement API Endpoints (requires authentication)');
  console.log('Note: These will return 401 Unauthorized without proper session cookies');
  
  // Test GET /api/achievements/me
  await testEndpoint('GET', '/api/achievements/me');
  
  // Test POST /api/achievements (create)
  console.log('\n📝 Testing Create Achievement:');
  const createdAchievement = await testEndpoint('POST', '/api/achievements', testAchievement);
  
  if (createdAchievement && createdAchievement._id) {
    const achievementId = createdAchievement._id;
    
    // Test GET /api/achievements/[id]
    console.log('\n📖 Testing Get Single Achievement:');
    await testEndpoint('GET', `/api/achievements/${achievementId}`);
    
    // Test PUT /api/achievements/[id] (update)
    console.log('\n✏️ Testing Update Achievement:');
    const updatedData = {
      ...testAchievement,
      title: "Updated Test Achievement",
      description: "This achievement has been updated by the test script"
    };
    await testEndpoint('PUT', `/api/achievements/${achievementId}`, updatedData);
    
    // Test DELETE /api/achievements/[id]
    console.log('\n🗑️ Testing Delete Achievement:');
    await testEndpoint('DELETE', `/api/achievements/${achievementId}`);
  }
  
  console.log('\n✅ CRUD Test completed');
  console.log('\nTo test with authentication:');
  console.log('1. Sign in to http://localhost:3001/auth/signin');
  console.log('2. Use the browser to test the achievements page');
  console.log('3. Try adding, editing, and deleting achievements through the UI');
}

main().catch(console.error);
