#!/usr/bin/env node

/**
 * Test Production API Endpoints
 * This script tests if the API endpoints are working in production
 */

const BASE_URL = 'https://www.achy.me';

const endpoints = [
  '/api/health',
  '/api/auth/providers',
  '/api/auth/csrf',
  '/api/notifications?limit=10',
  '/api/employees/me',
];

async function testEndpoint(endpoint) {
  try {
    console.log(`Testing ${BASE_URL}${endpoint}...`);
    const response = await fetch(`${BASE_URL}${endpoint}`);
    
    console.log(`  Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.text();
      console.log(`  Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
    } else {
      console.log(`  Error: ${response.status} - ${response.statusText}`);
    }
    
    console.log('');
  } catch (error) {
    console.log(`  Error: ${error.message}`);
    console.log('');
  }
}

async function main() {
  console.log('🔍 Testing Production API Endpoints\n');
  console.log(`Base URL: ${BASE_URL}\n`);
  
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
  }
  
  console.log('✅ Test completed');
  console.log('\nIf all endpoints return 404, the application may not be deployed correctly.');
  console.log('If health endpoint works but others fail, check environment variables in Vercel.');
}

main().catch(console.error);
