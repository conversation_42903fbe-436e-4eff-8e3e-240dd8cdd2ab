# Vercel Deployment Troubleshooting Guide

## 🚨 Current Issue: API Routes Returning 404

The 404 errors you're seeing are caused by missing environment variables and configuration issues. Here's how to fix them:

## ✅ Step-by-Step Fix

### 1. Set Environment Variables in Vercel

Go to your Vercel project dashboard → Settings → Environment Variables and add these:

**Required Variables:**
```
NEXTAUTH_URL=https://your-app-name.vercel.app
NEXTAUTH_SECRET=your-super-secret-key-here-minimum-32-characters
PRODUCTION_DOMAIN=your-app-name.vercel.app
MONGODB_URI=mongodb+srv://username:<EMAIL>/achy-me?retryWrites=true&w=majority
```

**For Image Uploads (R2 Storage):**
```
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
CLOUDFLARE_ACCESS_KEY_ID=your-r2-access-key-id
CLOUDFLARE_SECRET_ACCESS_KEY=your-r2-secret-access-key
CLOUDFLARE_BUCKET_NAME=achy-me-images
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
```

**Optional (for AI features):**
```
OPENAI_API_KEY=sk-your-openai-api-key-here
```

### 2. Generate NEXTAUTH_SECRET

Run this command to generate a secure secret:
```bash
openssl rand -base64 32
```

### 3. Set Correct NEXTAUTH_URL

Replace `your-app-name` with your actual Vercel app name:
- If your Vercel URL is `https://achy-abc123.vercel.app`, use that exact URL
- Make sure there's no trailing slash

### 4. Configure MongoDB

Ensure your MongoDB connection string:
- Has the correct username/password
- Allows connections from all IPs (0.0.0.0/0) in MongoDB Atlas
- Uses the correct database name (`achy-me`)

### 5. Redeploy After Setting Variables

After adding all environment variables:
1. Go to Vercel dashboard → Deployments
2. Click the three dots on the latest deployment
3. Click "Redeploy"

## 🔍 Common Issues & Solutions

### Issue: "Organization not found" errors
**Solution:** Make sure `PRODUCTION_DOMAIN` matches your Vercel domain exactly (without https://)

### Issue: Database connection fails
**Solution:** 
- Check MongoDB Atlas IP whitelist (allow 0.0.0.0/0)
- Verify connection string format
- Ensure database user has read/write permissions

### Issue: Authentication not working
**Solution:**
- Verify NEXTAUTH_SECRET is at least 32 characters
- Ensure NEXTAUTH_URL matches your Vercel domain exactly
- Check that credentials are correct

## 🧪 Testing the Fix

After redeployment, test these endpoints:
1. `/api/health` - Should return 200 OK
2. `/api/employees/me` - Should return 401 (unauthorized) or employee data if logged in
3. `/api/notifications` - Should return 401 or notifications data

## 📋 Environment Variables Checklist

- [ ] NEXTAUTH_URL (matches Vercel domain exactly)
- [ ] NEXTAUTH_SECRET (32+ characters)
- [ ] PRODUCTION_DOMAIN (Vercel domain without https://)
- [ ] MONGODB_URI (valid connection string)
- [ ] Cloudflare R2 variables (if using image uploads)
- [ ] All variables added to Vercel project settings
- [ ] Redeployed after adding variables

## 🆘 If Still Not Working

1. Check Vercel function logs:
   - Go to Vercel dashboard → Functions
   - Click on failing API routes to see error logs

2. Check browser network tab:
   - Look for the exact error messages
   - Verify the request URLs are correct

3. Test locally first:
   ```bash
   npm run dev
   ```
   If it works locally but not on Vercel, it's definitely an environment variable issue.
